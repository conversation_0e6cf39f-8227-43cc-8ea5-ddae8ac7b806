package com.noura.api.service.auth;

import com.noura.api.dto.mapper.auth.AuthMapper;
import com.noura.api.dto.mapper.users.UserMapper;
import com.noura.api.dto.request.auth.LoginRequest;
import com.noura.api.dto.request.auth.RegisterRequest;
import com.noura.api.dto.response.auth.AuthResponse;
import com.noura.api.dto.response.users.UserResponse;
import com.noura.api.exception.base.ErrorCode;
import com.noura.api.exception.custom.auth.EmailAlreadyExistsException;
import com.noura.api.exception.custom.auth.InvalidCredentialsException;
import com.noura.api.model.entity.User;
import com.noura.api.repository.users.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuthService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;
    private final UserMapper userMapper;
    private final AuthMapper authMapper;

    public AuthResponse login(LoginRequest request) {
        try {
            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getEmail(), request.getPassword())
            );

            User user = (User) authentication.getPrincipal();

            // Check if 2FA is enabled
            if (user.isTwoFactorEnabled()) {
                // Handle 2FA flow
                String sessionToken = generate2FASessionToken(user);
                return authMapper.toTwoFactorResponse(sessionToken, "2FA code required");
            }

            // Generate tokens
            String accessToken = jwtService.generateToken(user);
            String refreshToken = jwtService.generateRefreshToken(user);
            Long expiresIn = jwtService.getExpirationTime();

            log.info("User {} logged in successfully", user.getEmail());

            return authMapper.toAuthResponse(accessToken, refreshToken, expiresIn, user);

        } catch (BadCredentialsException e) {
            log.warn("Failed login attempt for email: {}", request.getEmail());
            throw new InvalidCredentialsException(ErrorCode.INVALID_CREDENTIALS.getMessage());
        }
    }

    public UserResponse register(RegisterRequest request) {
        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new EmailAlreadyExistsException("Email is already registered");
        }

        // Create user entity using mapper
        User user = userMapper.toUser(request);

        // Encode password
        user.setPassword(passwordEncoder.encode(request.getPassword()));

        // Save user
        User savedUser = userRepository.save(user);

        log.info("New user registered: {}", savedUser.getEmail());

        return userMapper.toUserResponse(savedUser);
    }

    public AuthResponse refreshToken(String refreshToken) {
        if (!jwtService.isTokenValid(refreshToken)) {
            throw new InvalidCredentialsException("Invalid refresh token");
        }

        String email = jwtService.extractUsername(refreshToken);
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new InvalidCredentialsException("User not found"));

        String newAccessToken = jwtService.generateToken(user);
        String newRefreshToken = jwtService.generateRefreshToken(user);
        Long expiresIn = jwtService.getExpirationTime();

        return authMapper.toAuthResponse(newAccessToken, newRefreshToken, expiresIn, user);
    }

    private String generate2FASessionToken(User user) {
        // Implementation for 2FA session token
        return "2fa_session_" + user.getId();
    }
}