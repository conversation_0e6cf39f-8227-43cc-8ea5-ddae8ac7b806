package com.noura.api.service.auth;

import com.noura.api.config.AppProperties;
import com.noura.api.model.entity.User;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Service
@RequiredArgsConstructor
@Slf4j
public class JwtService {


    private final AppProperties appProperties;  // ← Modern way: inject config

    // Generate token
    public String generateToken(User user) {
        return generateToken(new HashMap<>(), user);
    }

    public String generateToken(Map<String, Object> extraClaims, User user) {
        return buildToken(extraClaims, user, appProperties.getSecurity().getJwt().getExpiration());
    }

    public String generateRefreshToken(User user) {
        return buildToken(new HashMap<>(), user, appProperties.getSecurity().getJwt().getRefreshToken().getExpiration());
    }

    private String buildToken(Map<String, Object> extraClaims, User user, long expiration) {
        return Jwts
                .builder()
                .claims(extraClaims)
                .subject(user.getEmail())
                .claim("userId", user.getId())
                .claim("role", user.getRole().name())
                .issuedAt(new Date(System.currentTimeMillis()))
                .expiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(getSignInKey())
                .compact();
    }

    // Validate token
    public boolean isTokenValid(String token, User user) {
        try {
            final String username = extractUsername(token);
            return username.equals(user.getEmail()) && !isTokenExpired(token);
        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    public boolean isTokenValid(String token) {
        try {
            return !isTokenExpired(token);
        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    // Extract information from token
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Long extractUserId(String token) {
        return extractClaim(token, claims -> claims.get("userId", Long.class));
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    private Claims extractAllClaims(String token) {
        return Jwts
                .parser()
                .verifyWith(getSignInKey())
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    private SecretKey getSignInKey() {
        byte[] keyBytes = Decoders.BASE64.decode(appProperties.getSecurity().getJwt().getSecretKey());
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public long getExpirationTime() {
        return appProperties.getSecurity().getJwt().getExpiration() / 1000;
    }

    /**
     * Generate token with user ID in claims
     */
    public String generateTokenWithUserId(User user, Long userId) {
        Map<String, Object> extraClaims = new HashMap<>();
        extraClaims.put("userId", userId);
        return generateToken(extraClaims, user);
    }

    /**
     * Blacklist token (for logout functionality - blacklist storage mechanism missing)
     */
    public void blacklistToken(String token) {
        // Implementation depends on your blacklist storage strategy:
        // - Redis with expiration
        // - Database table
        // - In-memory cache
        log.info("Token blacklisted: {}", token.substring(0, 20) + "...");

        // Example with Redis (if you implement it):
        // redisTemplate.opsForValue().set("blacklist:" + token, "true",
        //     Duration.ofMilliseconds(getTokenRemainingTime(token) * 1000));
    }

    /**
     * Check if token is blacklisted
     */
    public boolean isTokenBlacklisted(String token) {
        // Implementation depends on your blacklist storage strategy
        // Example with Redis:
        // return Boolean.TRUE.equals(redisTemplate.hasKey("blacklist:" + token));

        return false; // Default: no blacklisting
    }
}