package com.noura.api.dto.response.auth;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.noura.api.dto.response.users.UserResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL) // Only include non-null fields in JSON
public class AuthResponse {

    // JWT tokens
    private String accessToken;
    private String refreshToken;

    // Token metadata
    @Builder.Default
    private String tokenType = "Bearer";
    private Long expiresIn; // Token expiration time in seconds

    // User information
    private UserResponse user;

    // 2FA related fields (optional)
    private Boolean requiresTwoFactor;
    private String sessionToken; // For 2FA flow
    private String message;

    // Additional metadata (optional)
    private String scope; // Token scope/permissions
}
