package com.noura.api.dto.request.Users;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUserRequest {

    @Size(max = 50, message = "First name cannot exceed 50 characters")
    private String firstName;

    @Size(max = 50, message = "Last name cannot exceed 50 characters")
    private String lastName;

    @Size(max = 15, message = "Phone number cannot exceed 15 characters")
    @Pattern(regexp = "^[+]?[0-9\\s\\-\\(\\)]{7,15}$", message = "Invalid phone number format")
    private String phoneNumber;

    // Optional: Add more fields as needed
    @Size(max = 200, message = "Bio cannot exceed 200 characters")
    private String bio;

    @Size(max = 100, message = "Job title cannot exceed 100 characters")
    private String jobTitle;

    @Size(max = 100, message = "Company cannot exceed 100 characters")
    private String company;

    @Size(max = 100, message = "Location cannot exceed 100 characters")
    private String location;

    @Pattern(regexp = "^https?://.*", message = "Website must be a valid URL")
    private String website;

    // Note: Email is intentionally NOT included for security reasons
    // Email changes should be handled by a separate endpoint with verification
}