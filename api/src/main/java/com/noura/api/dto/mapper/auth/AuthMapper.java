package com.noura.api.dto.mapper.auth;

import com.noura.api.dto.mapper.users.UserMapper;
import com.noura.api.dto.response.auth.AuthResponse;
import com.noura.api.model.entity.User;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {UserMapper.class} // Use UserMapper for user mapping
)
public interface AuthMapper {

    /**
     * Create AuthResponse for successful login
     */
    @Mapping(target = "user", source = "user")
    @Mapping(target = "tokenType", constant = "Bearer")
    @Mapping(target = "requiresTwoFactor", constant = "false")
    @Mapping(target = "sessionToken", ignore = true)
    @Mapping(target = "message", ignore = true)
    AuthResponse toAuthResponse(String accessToken, String refreshToken, Long expiresIn, User user);

    /**
     * Create AuthResponse for 2FA required scenario
     */
    @Mapping(target = "requiresTwoFactor", constant = "true")
    @Mapping(target = "accessToken", ignore = true)
    @Mapping(target = "refreshToken", ignore = true)
    @Mapping(target = "tokenType", ignore = true)
    @Mapping(target = "expiresIn", ignore = true)
    @Mapping(target = "user", ignore = true)
    AuthResponse toTwoFactorResponse(String sessionToken, String message);

    /**
     * Create AuthResponse for token refresh
     */
    @Mapping(target = "user", source = "user")
    @Mapping(target = "tokenType", constant = "Bearer")
    @Mapping(target = "requiresTwoFactor", constant = "false")
    @Mapping(target = "sessionToken", ignore = true)
    @Mapping(target = "message", ignore = true)
    AuthResponse toRefreshResponse(String accessToken, String refreshToken, Long expiresIn, User user);
}