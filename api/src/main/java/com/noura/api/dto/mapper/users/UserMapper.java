package com.noura.api.dto.mapper.users;

import com.noura.api.dto.request.Users.UpdateUserRequest;
import com.noura.api.dto.request.auth.RegisterRequest;
import com.noura.api.dto.response.users.UserResponse;
import com.noura.api.model.entity.User;
import org.mapstruct.*;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Mapper(
        componentModel = "spring",
        injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        imports = {LocalDateTime.class}
)
@Component
public interface UserMapper {

    // RESPONSE MAPPINGS
    // ========================================
    @Named("basic")
    @Mapping(target = "fullName", expression = "java(user.getFirstName() + \" \" + user.getLastName())")
    UserResponse toResponse(User user);

    @Named("detailed")
    @Mapping(target = "fullName", expression = "java(getFullName(user.getFirstName(), user.getLastName()))")
    UserResponse toDetailedResponse(User user);

    // Specify which method to use for the list
    @IterableMapping(qualifiedByName = "basic")
    List<UserResponse> toResponseList(List<User> users);

    // Add a detailed list method if needed
    @IterableMapping(qualifiedByName = "detailed")
    List<UserResponse> toDetailedResponseList(List<User> users);

    // ========================================

    // HELPERS
    // ========================================

    default String getFullName(String firstName, String lastName) {
        if (firstName == null && lastName == null) {
            return null;
        }
        return (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
    }

    // Auth
    // ========================================

    // ===== ENTITY TO DTO =====

    /**
     * Convert User entity to UserResponse DTO
     */
    @Mapping(target = "fullName", expression = "java(getFullName(user.getFirstName(), user.getLastName()))")
    UserResponse toUserResponse(User user);

    /**
     * Convert List<User> entities to List<UserResponse> DTOs
     */
    List<UserResponse> toUserResponseList(List<User> users);

    // ===== DTO TO ENTITY =====

    /**
     * Convert RegisterRequest DTO to User entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "password", ignore = true) // Password encoded separately for security
    @Mapping(target = "role", constant = "USER")
    @Mapping(target = "twoFactorEnabled", constant = "false")
    @Mapping(target = "twoFactorSecret", ignore = true)
    @Mapping(target = "backupCodes", ignore = true)
    @Mapping(target = "createdAt", expression = "java(LocalDateTime.now())")
    @Mapping(target = "updatedAt", expression = "java(LocalDateTime.now())")
    User toUser(RegisterRequest registerRequest);

    // ===== UPDATE ENTITY FROM DTO =====

    /**
     * Update existing User entity from UpdateUserRequest DTO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "email", ignore = true) // Don't allow email updates
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "role", ignore = true)
    @Mapping(target = "twoFactorEnabled", ignore = true)
    @Mapping(target = "twoFactorSecret", ignore = true)
    @Mapping(target = "backupCodes", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", expression = "java(LocalDateTime.now())")
    void updateUserFromRequest(UpdateUserRequest request, @MappingTarget User user);
}