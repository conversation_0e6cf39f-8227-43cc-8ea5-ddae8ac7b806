package com.noura.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "app")  // Configuration properties binding
@Data
public class AppProperties {

    private Cors cors = new Cors();
    private Security security = new Security();
    private Pagination pagination = new Pagination();

    @Data
    public static class Cors {
        private List<String> allowedOrigins;
    }

    @Data
    public static class Security {
        private Jwt jwt = new Jwt();

        @Data
        public static class Jwt {
            private String secretKey;
            private long expiration;
            private RefreshToken refreshToken = new RefreshToken();

            @Data
            public static class RefreshToken {
                private long expiration;
            }
        }
    }

    @Data
    public static class Pagination {
        private int defaultPageSize = 20;
        private int maxPageSize = 100;
    }
}