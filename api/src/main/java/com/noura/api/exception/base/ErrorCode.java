package com.noura.api.exception.base;

import lombok.Getter;

@Getter
public enum ErrorCode {
    // User related
    USER_NOT_FOUND("USR_01", "User not found"),
    INVALID_CREDENTIALS("USR_02", "Invalid email or password"),
    DUPLICATE_EMAIL("USR_03", "Email already exists"),

    // Permission related
    INSUFFICIENT_PERMISSIONS("SEC_01", "Insufficient permissions"),
    ACCESS_DENIED("SEC_02", "Access denied"),

    // Business logic
    BUSINESS_RULE_VIOLATION("BUS_01", "Business rule violation"),
    INVALID_OPERATION("BUS_02", "Invalid operation"),

    // System
    INTERNAL_ERROR("SYS_01", "Internal server error"),
    EXTERNAL_SERVICE_ERROR("SYS_02", "External service error");

    private final String code;
    private final String message;

    ErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

}