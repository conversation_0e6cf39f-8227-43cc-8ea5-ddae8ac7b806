package com.noura.api.controller.auth;

import com.noura.api.model.entity.User;
import com.noura.api.model.enums.Role;
import com.noura.api.repository.users.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
@Slf4j
public class SingleUserInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    // Configure your user here
    private static final String USER_EMAIL = "<EMAIL>";
    private static final String USER_PASSWORD = "12345678";

    @Override
    public void run(String... args) throws Exception {
        if (userRepository.existsByEmail(USER_EMAIL)) {
            log.info("ℹ️ User {} already exists, skipping", USER_EMAIL);
            return;
        }

        // Create user with properly encoded password
        String encodedPassword = passwordEncoder.encode(USER_PASSWORD);
        User user = User.builder()
                .firstName("Sam")
                .lastName("Wooster")
                .email(USER_EMAIL)
                .password(encodedPassword)  // ← Properly encoded!
                .role(Role.USER)
                .enabled(true)
                .twoFactorEnabled(false)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        userRepository.save(user);
        log.info("✅ User created: {} / {}", USER_EMAIL, USER_PASSWORD);
    }
}