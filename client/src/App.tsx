import {useEffect, useRef} from 'react'
import './App.css'
import {setToast} from "./services/utils/toastService.ts";
import {Toast} from "primereact/toast";
import AuthLayout from "./features/auth/components/AuthLayout/AuthLayout.tsx";
import {AuthProvider} from "./features/auth/context/AuthContext.tsx";
import {Navigate, Routes, Route, BrowserRouter} from 'react-router-dom';
import ProtectedRoute from "./features/auth/components/ProtectedRoute/ProtectedRoute.tsx";
import Dashboard from "./features/dashboard/components/Welcome.tsx";

function App() {
    const toast = useRef<Toast>(null);

    useEffect(() => {
        setToast((msg) => toast.current?.show(msg));
    }, []);

  return (

      <>

              <BrowserRouter>
                  <AuthProvider>
                  <div className="App">
                      <Routes>
                          <Route path="/auth" element={<AuthLayout />} />

                          <Route
                              path="/dashboard"
                              element={
                                  <ProtectedRoute>
                                      <Dashboard />
                                  </ProtectedRoute>
                              }
                          />

                          <Route path="/" element={<Navigate to="/dashboard" />} />
                      </Routes>

                      <Toast ref={toast}/>
                  </div>
                  </AuthProvider>
              </BrowserRouter>

      </>
  )
}

export default App
