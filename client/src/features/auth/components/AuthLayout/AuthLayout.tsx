import React, {useEffect, useState} from "react";
import {
  splitContainer,
  brandSide,
  brandGradient,
  glassOrb,
  orb1,
  orb2,
  orb3,
  drift,
  glassPanels,
  glassPanel,
  panel1,
  panel2,
  panelFloat,
  brandLogo,
  logoSquare,
} from "./AuthLayout.css";
import Auth from "../Auth/Auth.tsx";
import Welcome from "../Welcome/Welcome.tsx";
import {formSide} from "../Welcome/Welcome.css.ts";

const AuthLayout: React.FC = () => {
  const [display, setDisplay] = useState<boolean>(false);
  const [tab, setTab] = useState<boolean | null>(null);

  const handleDisplay = (value: boolean) => {
    setTab(value);
    setDisplay(true);
  }

  return (
      <div className={`${splitContainer} flex h-screen overflow-hidden`}>
        {/* Brand Side */}
        <div
            className={`${brandSide} ${brandGradient} flex-1 flex items-center justify-center relative overflow-hidden`}
        >
          {/* Glass Elements */}
          <div className="absolute inset-0">
            <div
                className={`${glassOrb} ${orb1} ${drift}`}
                style={{animationDelay: "0s"}}
            />
            <div
                className={`${glassOrb} ${orb2} ${drift}`}
                style={{animationDelay: "5s"}}
            />
            <div
                className={`${glassOrb} ${orb3} ${drift}`}
                style={{animationDelay: "10s"}}
            />

            <div
                className={`${glassPanels} absolute top-1/5 right-[10%] w-[120px] h-[200px]`}
            >
              <div className={`${glassPanel} ${panel1} ${panelFloat}`}/>
              <div
                  className={`${glassPanel} ${panel2} ${panelFloat}`}
                  style={{animationDirection: "reverse"}}
              />
            </div>
          </div>

          {/* Brand Content */}
          <div className="text-center text-white z-10 relative max-w-[380px]">
            <div
                className={`${brandLogo} w-[75px] h-[75px] mx-auto mb-7 flex items-center justify-center rounded-[20px] border border-white/20 shadow-lg`}
            >
              <div className="grid grid-cols-2 gap-[5px]">
                <div className={logoSquare}/>
                <div className={logoSquare}/>
                <div className={logoSquare}/>
                <div className={logoSquare}/>
              </div>
            </div>
            <h1 className="text-[30px] font-semibold mb-[14px] tracking-[-0.4px] text-shadow-md">
              Noura Health
            </h1>
            <p className="text-[15px] opacity-95 leading-relaxed">
              Empowering Lives Through Better Health and Trusted Care, Supporting
              You Every Step Toward a Healthier Future.
            </p>
          </div>
        </div>

        {/* Form Side */}
        <div className={`${formSide} flex-1 flex items-center justify-center py-[50px] px-10 relative`}>
          {display ? <Auth tab={tab}/> : <Welcome onNext={handleDisplay}/>}
        </div>
      </div>
  );
};

export default AuthLayout;
