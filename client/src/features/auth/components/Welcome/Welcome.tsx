import {useEffect} from "react";
import {
  formBackdrop,
  formContainer,
  formLogo,
  formLogoSquare,
  btnPrimary,
  btnSecondary,
  dividerLine,
  dividerSpan,
} from "./Welcome.css";
import {useQuery} from "@tanstack/react-query";
import {getUsers, type User} from "../../../../services/userService.ts";

const Welcome  = ({ onNext }: {onNext: (value: boolean) => void}) => {

  // //TODO: Remove, its just for testing purpose.
  // const { data } = useQuery<User[]>({
  //   queryKey: ["users"],
  //   queryFn: getUsers,
  // });
  //
  // useEffect(() => {
  //   if (data) {
  //     console.log("Users:", data);
  //   }
  // }, [data]);

  return (
      <>
        <div className={formBackdrop}/>

        <div
            className={`${formContainer} w-full max-w-[400px] py-[45px] px-10 rounded-2xl border border-white/30 relative z-10`}
        >
          {/* Form Header */}
          <div className="text-center mb-[35px]">
            <div
                className={`${formLogo} w-[52px] h-[52px] mx-auto mb-[22px] flex items-center justify-center rounded-[14px] border border-black/8`}
            >
              <div className="grid grid-cols-2 gap-1">
                <div className={formLogoSquare}/>
                <div className={formLogoSquare}/>
                <div className={formLogoSquare}/>
                <div className={formLogoSquare}/>
              </div>
            </div>
            <h2 className="text-gray-900 text-2xl font-semibold mb-2">
              Welcome back
            </h2>
            <p className="text-gray-500 text-sm">Sign in to your account</p>
          </div>

          {/* Buttons */}
          <button
              onClick={() => onNext(true)}
              className={`${btnPrimary} w-full py-4 px-[22px] my-[10px] rounded-[10px] text-[15px] font-medium cursor-pointer transition-all duration-300 border`}
          >
            Sign In
          </button>

          <div className="my-6 text-gray-500 text-[13px] relative text-center">
            <div className={dividerLine}/>
            <span className={dividerSpan}>or</span>
          </div>

          <button
              onClick={() => onNext(false)}
              className={`${btnSecondary} w-full py-4 px-[22px] my-[10px] rounded-[10px] text-[15px] font-medium cursor-pointer transition-all duration-300 border`}
          >
            Create Account
          </button>

          {/* Footer */}
          <div className="mt-6 text-xs text-gray-400 text-center">
            Secured with{" "}
            <a href="#" className="text-indigo-500 hover:underline">
              enterprise security
            </a>
          </div>
        </div>
      </>
  );
};

export default Welcome;
