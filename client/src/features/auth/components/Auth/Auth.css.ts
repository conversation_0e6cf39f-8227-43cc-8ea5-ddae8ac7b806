import { globalStyle} from "@vanilla-extract/css";

globalStyle(
    "button:hover", {
  cursor: "pointer",
  }
);

globalStyle(
    ".p-icon-field > .p-input-icon", {
      marginTop: "-0.85rem",
    },
);

export const vanillaExtractStyles = {
  submitButtonGradient: {
    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
    position: 'relative',
    overflow: 'hidden',
  },
  submitButtonHover: {
    background: 'linear-gradient(135deg, #5a67d8 0%, #667eea 100%)',
  },
  oauthButtonShimmer: {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.05), transparent)',
    transition: 'left 0.5s ease',
  },
  featuresGradient: {
    background: 'linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(99, 102, 241, 0.05))',
  },
  fadeInAnimation: {
    opacity: 0,
    transform: 'translateY(8px)',
    animation: 'fadeIn 0.4s ease-out forwards',
  },
  keyframes: `
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(8px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
       .p-inputtext.p-invalid {
      border-color: #ef4444 !important;
      box-shadow: 0 0 0 1px #ef4444 !important;
    }
    
    .p-password.p-invalid .p-inputtext {
      border-color: #ef4444 !important;
      box-shadow: 0 0 0 1px #ef4444 !important;
    }
    
    .p-checkbox.p-invalid .p-checkbox-box {
      border-color: #ef4444 !important;
    }
    
    .custom-input-wrapper {
      margin-bottom: 1.5rem;
    }
    
    .custom-input-label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 0.5rem;
    }
    
    .custom-input {
      width: 100%;
      padding: 1rem 1.25rem;
      border-radius: 0.75rem;
      font-size: 1rem;
      transition: all 0.3s ease;
    }
    
    .custom-input:focus {
      border-color: #6366f1;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      background-color: rgba(99, 102, 241, 0.02);
    }
    
    .custom-password {
      width: 100%;
    }
    
    .custom-password .p-inputtext {
      width: 100%;
      padding: 1rem 12.5rem 1rem 1.25rem;
      border-radius: 0.75rem;
      font-size: 1rem;
      transition: all 0.3s ease;
    }
    
    .custom-password .p-inputtext:focus {
      border-color: #6366f1;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
      background-color: rgba(99, 102, 241, 0.02);
    }
    
    .custom-checkbox {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      cursor: pointer;
    }
    
    .custom-checkbox-box {
      margin-top: 0.125rem;
    }
    
    .custom-checkbox-label {
      font-size: 0.875rem;
      color: #6b7280;
      line-height: 1.6;
    }
    
    .error-message {
      margin-top: 0.5rem;
      font-size: 0.875rem;
      color: #ef4444;
    }
  `,
};

