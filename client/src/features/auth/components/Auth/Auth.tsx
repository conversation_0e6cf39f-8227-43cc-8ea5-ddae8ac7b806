import React, { useState } from 'react';
import {Mail, Check } from 'lucide-react';
import {vanillaExtractStyles} from "./Auth.css";
import {
    type Control,
    Controller,
    type FieldError,
    type RegisterOptions,
    useForm,
} from "react-hook-form";
import {<PERSON><PERSON>} from "primereact/button";
import {Checkbox} from "primereact/checkbox";
import {InputText} from "primereact/inputtext";
import {Password} from "primereact/password";
import {toastService} from "../../../../services/utils/toastService.ts";
import {useAuth} from "../../context/AuthContext.tsx";

interface SignInFormData {
    email: string;
    password: string;
    rememberMe: boolean;
}

interface SignUpFormData {
    email: string;
    password: string;
    confirmPassword: string;
    acceptTerms: boolean;
}

interface ShowPassword {
    signin: boolean;
    signup: boolean;
    confirm: boolean;
}

interface OAuthButtonProps {
    icon: React.ReactNode;
    text: string;
    provider: string;
    action: string;
}

interface PrimeInputFieldProps {
    label: string;
    name: keyof SignInFormData | keyof SignUpFormData;
    placeholder: string;
    feedback?: boolean;
    control: Control<any>;
    error?: FieldError;
    icon?: React.ReactNode;
    type?: 'text' | 'email' | 'password';
    showPasswordToggle?: boolean;
    onTogglePassword?: () => void;
    showPassword?: boolean;
    rules?: RegisterOptions;
}

interface FeatureItemProps {
    text: string;
}

interface PrimeCheckboxProps {
    label: React.ReactNode;
    name: keyof SignInFormData | keyof SignUpFormData;
    control: any;
    error?: FieldError;
    rules?: RegisterOptions;
}

const GoogleIcon: React.FC = () => (
    <svg className="w-5 h-5" viewBox="0 0 24 24">
      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
    </svg>
);

const OutlookIcon: React.FC = () => (
    <svg  width="20" height="20" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" fill="#000000">
        <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
        <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
        <g id="SVGRepo_iconCarrier"><title>file_type_outlook</title>
            <path d="M19.484,7.937v5.477L21.4,14.619a.489.489,0,0,0,.21,0l8.238-5.554a1.174,1.174,0,0,0-.959-1.128Z"
                  style={{fill: "#0072c6"}}></path>
            <path
                d="M19.484,15.457l1.747,1.2a.522.522,0,0,0,.543,0c-.3.181,8.073-5.378,8.073-5.378V21.345a1.408,1.408,0,0,1-1.49,1.555H19.483V15.457Z"
                style={{fill: "#0072c6"}}></path>
            <path
                d="M10.44,12.932a1.609,1.609,0,0,0-1.42.838,4.131,4.131,0,0,0-.526,2.218A4.05,4.05,0,0,0,9.02,18.2a1.6,1.6,0,0,0,2.771.022,4.014,4.014,0,0,0,.515-2.2,4.369,4.369,0,0,0-.5-2.281A1.536,1.536,0,0,0,10.44,12.932Z"
                    style={{fill: "#0072c6"}}></path>
            <path
                d="M2.153,5.155V26.582L18.453,30V2ZM13.061,19.491a3.231,3.231,0,0,1-2.7,1.361,3.19,3.19,0,0,1-2.64-1.318A5.459,5.459,0,0,1,6.706,16.1a5.868,5.868,0,0,1,1.036-3.616A3.267,3.267,0,0,1,10.486,11.1a3.116,3.116,0,0,1,2.61,1.321,5.639,5.639,0,0,1,1,3.484A5.763,5.763,0,0,1,13.061,19.491Z"
                style={{fill: "#0072c6"}}></path>
        </g>
    </svg>
);

const Auth = ({tab}: {tab: boolean | null}) => {
    const [activeTab, setActiveTab] = useState<'signin' | 'signup'>(tab ? 'signin' : 'signup');
    const [showPassword, setShowPassword] = useState<ShowPassword>({
        signin: false,
        signup: false,
        confirm: false
    });

    // const [email, setEmail] = useState('');
    // const [password, setPassword] = useState('');
    const { login, isLoggingIn, loginError } = useAuth();

    const onSignInSubmit = async (data: { email: string; password: string }) => {
        // Basic validation
        if (!data.email || !data.password) {
            toastService.error('Email or password is missing.')
            return;
        }

        try {
            await login(data.email, data.password);
            // Success handling is done in AuthContext (redirect, toast, etc.)
        } catch (error: any) {
            toastService.error(error.message)
        }
    };

    const {
        control: signinControl,
        handleSubmit: signinHandleSubmit,
        formState: { errors: signinErrors }
    } = useForm<SignInFormData>({
        mode: 'onBlur',
        reValidateMode: 'onBlur',
        defaultValues: {
            email: '',
            password: '',
            rememberMe: false
        }
    });

    const {
        control: signupControl,
        handleSubmit: signupHandleSubmit,
        formState: { errors: signupErrors },
        watch: signupWatch // Only kept this one because it's actually used for acceptTermsValue
    } = useForm<SignUpFormData>({
        mode: 'onBlur',
        reValidateMode: 'onBlur',
        defaultValues: {
            email: '',
            password: ''
        }
    });

    const handleTabSwitch = (tab: 'signin' | 'signup'): void => {
        setActiveTab(tab);
    };

    const togglePassword = (field: keyof ShowPassword): void => {
        setShowPassword(prev => ({
            ...prev,
            [field]: !prev[field]
        }));
    };

    const handleOAuthClick = (provider: string, action: string): void => {
        alert(`${action === 'signin' ? 'Signing in' : 'Creating account'} with ${provider.charAt(0).toUpperCase() + provider.slice(1)}...\n\nRedirecting to ${provider} authentication.`);
    };

    const onSignUpSubmit = (data: SignUpFormData): void => {
        console.log('Sign up data:', data);
        alert('🎉 Welcome to Noura Health!\n\nYour account has been created successfully.');
    };

    const handleForgotPassword = (): void => {
        alert('Password reset link sent to your email!');
    };

    // Validation rules
    const emailValidation: RegisterOptions = {
        required: 'Email is required',
        pattern: {
            value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: 'Please enter a valid email address'
        }
    };

    const passwordValidation: RegisterOptions = {
        required: 'Password is required',
        minLength: {
            value: 8,
            message: 'Password must be at least 8 characters'
        },
        validate: (value: string) => {
            if (!/[A-Z]/.test(value)) return 'Password must contain at least one uppercase letter';
            if (!/[0-9]/.test(value)) return 'Password must contain at least one number';
            return true;
        }
    };

    // OAuth Button Component with vanilla-extract shimmer effect
    const OAuthButton: React.FC<OAuthButtonProps> = ({ icon, text, provider, action }) => {
        const [isHovered, setIsHovered] = useState<boolean>(false);

        return (
            <button
                onClick={() => handleOAuthClick(provider, action)}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                className="flex-1 flex items-center justify-center gap-3 px-4 py-3.5 border-2 border-gray-200 rounded-xl bg-white text-gray-700 font-semibold text-sm transition-all duration-300 hover:border-indigo-500 hover:bg-indigo-50 hover:-translate-y-0.5 hover:shadow-md relative overflow-hidden"
            >
                <div
                    className={`${vanillaExtractStyles.oauthButtonShimmer} absolute inset-0 transition-transform duration-500`}
                    style={{
                        left: isHovered ? '100%' : '-100%'
                    }}
                />
                {icon}
                <span className="relative z-10">{text}</span>
            </button>
        );
    };


    const PrimeInputField: React.FC<PrimeInputFieldProps> = ({
                                                                 label,
                                                                 name,
                                                                 placeholder,
                                                                 control,
                                                                 feedback,
                                                                 error,
                                                                 icon,
                                                                 type = 'text',
                                                                 showPasswordToggle = false,
                                                                 onTogglePassword,
                                                                 showPassword = false,
                                                                 rules
                                                             }) => (
        <div className="custom-input-wrapper">
            <label className="custom-input-label">
                {label}
            </label>
            <div className="relative">
                <Controller
                    name={name}
                    control={control}
                    rules={rules}
                    render={({ field, fieldState }) => (
                        <>
                            {type === 'password' || showPasswordToggle ? (
                                <Password
                                    {...field}
                                    value={field.value || ''}
                                    placeholder={placeholder}
                                    feedback={feedback}
                                    className={`custom-password ${fieldState.error ? 'p-invalid' : ''}`}
                                    inputClassName="custom-input"
                                    toggleMask={showPasswordToggle}
                                    onBlur={field.onBlur}
                                />
                            ) : (
                                <InputText
                                    {...field}
                                    value={field.value || ''}
                                    type={type}
                                    placeholder={placeholder}
                                    className={`custom-input ${fieldState.error ? 'p-invalid' : ''}`}
                                    onBlur={field.onBlur}
                                />
                            )}
                        </>
                    )}
                />
                {icon && !showPasswordToggle && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        {icon}
                    </div>
                )}
            </div>
            {error && (
                <div className="error-message">
                    {error.message}
                </div>
            )}
        </div>
    );

    // FIXED: PrimeReact Checkbox Component with proper TypeScript types
    const PrimeCheckbox: React.FC<PrimeCheckboxProps> = ({ label, name, control, error, rules }) => (
        <div className="custom-checkbox-wrapper">
            <Controller
                name={name}
                control={control}
                rules={rules}
                render={({ field, fieldState }) => (
                    <label className="custom-checkbox">
                        <Checkbox
                            {...field}
                            checked={field.value || false}
                            className={`custom-checkbox-box ${fieldState.error ? 'p-invalid' : ''}`}
                            onChange={(e) => field.onChange(e.checked)}
                            onBlur={field.onBlur}
                        />
                        <div className="custom-checkbox-label">
                            {label}
                        </div>
                    </label>
                )}
            />
            {error && (
                <div className="error-message">
                    {error.message}
                </div>
            )}
        </div>
    );

    // Feature Item Component
    const FeatureItem: React.FC<FeatureItemProps> = ({ text }) => (
        <div className="flex items-center gap-3 mb-3 last:mb-0">
            <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0">
                <Check size={12} className="text-white font-bold" />
            </div>
            <span className="text-sm text-gray-900">{text}</span>
        </div>
    );

    // Watch the acceptTerms value for button state - Only used watch function
    const acceptTermsValue = signupWatch('acceptTerms');

    return (
        <>
            <style>{vanillaExtractStyles.keyframes}</style>
            <div className="min-h-screen flex items-center justify-center p-5">
                <div className="bg-white rounded-3xl overflow-hidden w-full max-w-lg shadow-2xl border border-gray-100">
                    {/* Tabs Header */}
                    <div className="flex bg-gray-50 border-b border-gray-200">
                        <button
                            onClick={() => handleTabSwitch('signin')}
                            className={`flex-1 py-5 text-center font-semibold transition-all duration-300 border-b-3 ${
                                activeTab === 'signin'
                                    ? 'text-indigo-600 bg-white border-indigo-600'
                                    : 'text-gray-500 border-transparent hover:bg-gray-100 hover:text-gray-600'
                            }`}
                        >
                            Sign In
                        </button>
                        <button
                            onClick={() => handleTabSwitch('signup')}
                            className={`flex-1 py-5 text-center font-semibold transition-all duration-300 border-b-3 ${
                                activeTab === 'signup'
                                    ? 'text-indigo-600 bg-white border-indigo-600'
                                    : 'text-gray-500 border-transparent hover:bg-gray-100 hover:text-gray-600'
                            }`}
                        >
                            Create Account
                        </button>
                    </div>

                    {/* Tab Content */}
                    <div className="p-10">
                        {activeTab === 'signin' && (
                            <div style={vanillaExtractStyles.fadeInAnimation}>
                                {/* Header */}
                                <div className="text-center mb-8">
                                    <h2 className="text-3xl font-bold text-gray-900 mb-2">Welcome Back</h2>
                                    <p className="text-gray-600">Sign in to your Noura Health account</p>
                                </div>

                                {/* OAuth Buttons */}
                                <div className="mb-8">
                                    <div className="flex gap-3 mb-6">
                                        <OAuthButton
                                            icon={<GoogleIcon />}
                                            text="Google"
                                            provider="google"
                                            action="signin"
                                        />
                                        <OAuthButton
                                            icon={<OutlookIcon />}
                                            text="Outlook"
                                            provider="outlook"
                                            action="signin"
                                        />
                                    </div>

                                    <div className="flex items-center my-6 text-gray-500 text-sm">
                                        <div className="flex-1 h-px bg-gray-200"></div>
                                        <span className="px-5 bg-white">or sign in with email</span>
                                        <div className="flex-1 h-px bg-gray-200"></div>
                                    </div>
                                </div>

                                {/* Sign In Form */}
                                <form onSubmit={signinHandleSubmit(onSignInSubmit)}>
                                    <PrimeInputField
                                        label="Email Address"
                                        name="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        control={signinControl}
                                        error={signinErrors.email}
                                        icon={<Mail size={18} />}
                                        rules={emailValidation}
                                    />

                                    <PrimeInputField
                                        label="Password"
                                        name="password"
                                        type="password"
                                        placeholder="Enter your password"
                                        control={signinControl}
                                        error={signinErrors.password}
                                        showPasswordToggle
                                        feedback={false}
                                        showPassword={showPassword.signin}
                                        onTogglePassword={() => togglePassword('signin')}
                                        rules={{ required: 'Password is required' }}
                                    />

                                    <PrimeCheckbox
                                        label="Remember Me"
                                        name="rememberMe"
                                        control={signinControl}
                                    />

                                    <div className="mt-6 mb-5">
                                        <Button
                                            type="submit"
                                            label="Sign In"
                                            className="w-full py-4 font-semibold rounded-xl transition-all duration-300 text-white"
                                            style={{
                                                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                                                border: 'none',
                                                borderRadius: '0.75rem',
                                                padding: '1rem'
                                            }}
                                        />
                                    </div>

                                    <div className="text-center">
                                        <Button
                                            type="button"
                                            label="Forgot your password?"
                                            link
                                            onClick={handleForgotPassword}
                                            className="text-indigo-600 text-sm font-medium hover:text-indigo-700 hover:underline"
                                        />
                                    </div>
                                </form>
                            </div>
                        )}

                        {activeTab === 'signup' && (
                            <div style={vanillaExtractStyles.fadeInAnimation}>
                                {/* Header */}
                                <div className="text-center mb-8">
                                    <h2 className="text-3xl font-bold text-gray-900 mb-2">Create Account</h2>
                                    <p className="text-gray-600">Join thousands of users who trust Noura Health</p>
                                </div>

                                {/* Sign Up Form */}
                                <form onSubmit={signupHandleSubmit(onSignUpSubmit)}>
                                    <PrimeInputField
                                        label="Email Address"
                                        name="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        control={signupControl}
                                        error={signupErrors.email}
                                        icon={<Mail size={18} />}
                                        rules={emailValidation}
                                    />

                                    <PrimeInputField
                                        label="Password"
                                        name="password"
                                        type="password"
                                        feedback={true}
                                        placeholder="Create a password"
                                        control={signupControl}
                                        error={signupErrors.password}
                                        showPasswordToggle
                                        showPassword={showPassword.signup}
                                        onTogglePassword={() => togglePassword('signup')}
                                        rules={passwordValidation}
                                    />

                                    <PrimeInputField
                                        label="Confirm Password"
                                        name="confirmPassword"
                                        type="password"
                                        feedback={true}
                                        placeholder="Confirm your password"
                                        control={signupControl}
                                        error={signupErrors.confirmPassword}
                                        showPasswordToggle
                                        showPassword={showPassword.confirm}
                                        onTogglePassword={() => togglePassword('confirm')}
                                        rules={{
                                            required: 'Please confirm your password',
                                            validate: (value: string) => {
                                                const password = signupWatch('password') || '';
                                                return value === password || 'Passwords do not match';
                                            }
                                        }}
                                    />

                                    {/* Features List with vanilla-extract gradient */}
                                    <div
                                        className="border border-emerald-100 rounded-xl p-5 mb-5"
                                        style={vanillaExtractStyles.featuresGradient}
                                    >
                                        <PrimeCheckbox
                                            label={
                                                <>
                                                    I agree to the{' '}
                                                    <a
                                                        href="#"
                                                        className="text-indigo-600 font-semibold hover:text-indigo-700 hover:underline"
                                                        onClick={(e: React.MouseEvent<HTMLAnchorElement>) => e.stopPropagation()}
                                                    >
                                                        Terms of Service
                                                    </a>{' '}
                                                    and{' '}
                                                    <a
                                                        href="#"
                                                        className="text-indigo-600 font-semibold hover:text-indigo-700 hover:underline"
                                                        onClick={(e: React.MouseEvent<HTMLAnchorElement>) => e.stopPropagation()}
                                                    >
                                                        Privacy Policy
                                                    </a>
                                                </>
                                            }
                                            name="acceptTerms"
                                            control={signupControl}
                                            error={signupErrors.acceptTerms}
                                            rules={{ required: 'You must accept the terms and conditions' }}
                                        />
                                    </div>

                                    <Button
                                        type="submit"
                                        label="Create Account"
                                        disabled={!acceptTermsValue}
                                        className="w-full py-4 font-semibold rounded-xl transition-all duration-300 text-white"
                                        style={{
                                            background: acceptTermsValue
                                                ? 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)'
                                                : '#d1d5db',
                                            border: 'none',
                                            borderRadius: '0.75rem',
                                            padding: '1rem',
                                            cursor: acceptTermsValue ? 'pointer' : 'not-allowed'
                                        }}
                                    />
                                </form>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </>
    );
};

export default Auth;