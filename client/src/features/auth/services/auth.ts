import apiClient from '../api/api.ts';
import { User } from '../types/user.ts';
import { LoginRequest, RegisterRequest, AuthResponse } from '../types/auth.ts';

export const authApi = {
    // Login
    login: async (credentials: LoginRequest): Promise<AuthResponse> => {
        const response = await apiClient.post('/auth/login', credentials);
        console.log('Login response:', response.data);
        return response.data;
    },

    // Register
    register: async (userData: RegisterRequest): Promise<User> => {
        const response = await apiClient.post('/auth/register', userData);
        return response.data;
    },

    // Refresh token
    refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
        const response = await apiClient.post('/auth/refresh', { refreshToken });
        return response.data;
    },

    // Logout
    logout: async (): Promise<void> => {
        await apiClient.post('/auth/logout');
    },

    // Get current user profile
    getProfile: async (): Promise<User> => {
        const response = await apiClient.get('/users/profile');
        return response.data;
    },
};