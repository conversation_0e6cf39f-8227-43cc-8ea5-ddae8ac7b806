export interface LoginRequest {
    email: string;
    password: string;
}

export interface RegisterRequest {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
}

export interface AuthResponse {
    accessToken: string;
    refreshToken: string;
    tokenType: string;
    expiresIn: number;
    user: User;
    requiresTwoFactor?: boolean;
    sessionToken?: string;
    message?: string;
}

export interface User {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    role: 'USER' | 'ADMIN';
    twoFactorEnabled: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface ApiError {
    status: number;
    error: string;
    message: string;
    path: string;
    timestamp: string;
    fieldErrors?: Array<{
        field: string;
        message: string;
        rejectedValue: any;
    }>;
}