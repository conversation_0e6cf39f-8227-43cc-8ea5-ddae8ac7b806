import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authApi } from '../services/auth';
import { tokenStorage } from '../api/api';
import { toastService } from "../../../services/utils/toastService.ts";

export const useLogin = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: authApi.login,
        onSuccess: (data) => {
            if (data.requiresTwoFactor) {
                // Handle 2FA flow
                toastService.success("2FA code required");
                // redirect to 2FA page here
            } else {
                // Store tokens and user
                tokenStorage.setTokens(data.accessToken, data.refreshToken);
                tokenStorage.setUser(data.user);

                // Invalidate and refetch user data
                queryClient.invalidateQueries({ queryKey: ['user'] });

                toastService.success('Login successful!');
            }
        },
        onError: (error: any) => {
            const message = error.response?.data?.message || 'Login failed';
            toastService.error(message);
        },
    });
};

export const useRegister = () => {
    return useMutation({
        mutationFn: authApi.register,
        onSuccess: () => {
            toastService.success('Registration successful! Please login.');
        },
        onError: (error: any) => {
            const message = error.response?.data?.message || 'Registration failed';
            toastService.error(message);
        },
    });
};

export const useLogout = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: authApi.logout,
        onSuccess: () => {
            tokenStorage.clearTokens();
            queryClient.clear(); // Clear all cached data
            toastService.success('Logged out successfully');
        },
        onError: () => {
            // Even if API call fails, clear local storage
            tokenStorage.clearTokens();
            queryClient.clear();
        },
    });
};

export const useCurrentUser = () => {
    return useQuery({
        queryKey: ['user'],
        queryFn: authApi.getProfile,
        enabled: !!tokenStorage.getAccessToken(), // Only run if token exists
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: false,
    });
};