import axios from "axios";
import {environment} from "../config/environment.ts";

const api = axios.create({
    baseURL: environment.apiUrl,
    // headers: {"Authorization": "Basic " + btoa("admin:admin")},
    timeout: 10000, // opcional, corta requests largos
});

// interceptor
api.interceptors.request.use((config) => {
    // token JWT
    // config.headers.Authorization = `Bearer ${localStorage.getItem("token")}`;
    return config;
});

export default api;
