import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
//import {AuthPage} from "./components/Auth/AuthPage.tsx";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
// import Auth2 from "./features/auth/components/Auth2/Auth.tsx";
import {PrimeReactProvider} from "primereact/api";
import "primereact/resources/themes/lara-light-cyan/theme.css";
import App from "./App.tsx";

const queryClient = new QueryClient();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
      <QueryClientProvider client={queryClient}>
          <PrimeReactProvider>
            <App />
          </PrimeReactProvider>
      </QueryClientProvider>
  </StrictMode>,
);
