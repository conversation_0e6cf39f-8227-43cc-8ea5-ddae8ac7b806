import { ToastMessage } from "primereact/toast";

let showToast: ((msg: ToastMessage) => void) | null = null;

export const setToast = (fn: (msg: ToastMessage) => void) => {
    showToast = fn;
};

export const toastService = {
    success: (detail: string) =>
        showToast?.({ severity: "success", detail, life: 3000 }),
    error: (detail: string) =>
        showToast?.({ severity: "error", detail, life: 3000 }),
    info: (detail: string) =>
        showToast?.({ severity: "info", detail, life: 3000 }),
    warn: (detail: string) =>
        showToast?.({ severity: "warn", detail, life: 3000 }),
};
